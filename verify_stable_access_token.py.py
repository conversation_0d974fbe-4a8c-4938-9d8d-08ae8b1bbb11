'''
Author: <PERSON>.<EMAIL>
Date: 2025-04-09 13:59:31
LastEditors: <PERSON>.<EMAIL>
LastEditTime: 2025-05-28 16:09:17
FilePath: /daily/access_token.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import requests
import json
from datetime import datetime

# 小程序的 AppID 和 Secret（请替换为实际值）
APPID = "wx674b60a859676717"
SECRET = "250fd1499c812616fe4b0caab9dffb0f"

# 1. 获取稳定的 access_token
def get_stable_access_token(appid, secret):
    url = "https://api.weixin.qq.com/cgi-bin/stable_token"
    payload = {
        "grant_type": "client_credential",
        "appid": appid,
        "secret": secret,
        "force_refresh": False  # 是否强制刷新，False 表示优先使用缓存
    }
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, data=json.dumps(payload), headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        result = response.json()
        
        access_token = result["access_token"]
        expires_in = result["expires_in"]  # 有效期，单位秒
        print(f"获取 access_token 成功: {access_token}, 有效期: {expires_in}秒")
        return access_token
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return None

# 2. 获取小程序某个付费能力的最近用量数据
def get_recent_average_usage(access_token, spu_id):
    url = "https://api.weixin.qq.com/wxa/charge/usage/get_recent_average"
    params = {
        "access_token": access_token,
        "spuId": spu_id  # 商品 SPU ID
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        
        if result.get("errcode") == 0:
            average_data = result["averageData"]
            print(f"使用accessToken 获取最近用量成功: 月平均用量约为 {average_data} 次")
            return average_data
        else:
            print(f"获取用量失败: {result}")
            return None
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return None
    

# 主函数：执行流程
def main():
    # 获取 access_token
    # access_token = get_stable_access_token(APPID, SECRET)
    # if not access_token:
    #     return
    
    # 使用 access_token 获取组件用量 看看是否合法正确获取
    component_type = "10000077"
    access_token = "92_uJjCot-mhTJHEp-xUIOG6SD83I9K_SFYdi2GgVz20U2EKUeb8rTk07WV7e7nFa-iVKCPf9czK62_EMqVxLyySf-y6ifELLM9JwXmXjaMaLqIQ8X7kKu-7MHuK0gQFJdAEACRV"
    get_recent_average_usage(access_token, component_type)
    

if __name__ == "__main__":
    main()