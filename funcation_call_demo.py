import json
import re
from ds_demo import call_llm

# 模拟的天气数据
def get_weather(city):
    if city is None:
        raise ValueError("城市不能为空")
    
    mock_weather = {
        "北京": {
            "temperature": "28",
            "weather": "多云"
        },
        "上海": {
            "temperature": "32",
            "weather": "晴天"
        },
        "广州": {
            "temperature": "25",
            "weather": "雨天"
        },
        "深圳": {
            "temperature": "20",
            "weather": "下雪"
        },
        "杭州": {
            "temperature": "26",
            "weather": "多云"
        }
    }
    return mock_weather.get(city)

# 模拟的天气预报数据
def get_forecast(city, days=3):
    if city is None:
        raise ValueError("城市不能为空")
    if not isinstance(days, int) or days < 1 or days > 3:
        days = 3  # 默认 3 天，限制最大 3 天
    
    mock_forecast = {
        "北京": [
            {"day": "明天", "temperature": "29", "weather": "晴天"},
            {"day": "后天", "temperature": "27", "weather": "多云"},
            {"day": "大后天", "temperature": "26", "weather": "小雨"}
        ],
        "上海": [
            {"day": "明天", "temperature": "33", "weather": "晴天"},
            {"day": "后天", "temperature": "31", "weather": "多云"},
            {"day": "大后天", "temperature": "30", "weather": "晴天"}
        ],
        "广州": [
            {"day": "明天", "temperature": "26", "weather": "大雨"},
            {"day": "后天", "temperature": "25", "weather": "中雨"},
            {"day": "大后天", "temperature": "27", "weather": "多云"}
        ],
        "深圳": [
            {"day": "明天", "temperature": "21", "weather": "小雪"},
            {"day": "后天", "temperature": "22", "weather": "多云"},
            {"day": "大后天", "temperature": "20", "weather": "晴天"}
        ],
        "杭州": [
            {"day": "明天", "temperature": "27", "weather": "多云"},
            {"day": "后天", "temperature": "25", "weather": "晴天"},
            {"day": "大后天", "temperature": "26", "weather": "小雨"}
        ]
    }
    forecast = mock_forecast.get(city)
    if forecast:
        return forecast[:days]  # 返回指定天数的预报
    return None

# 清洗 LLM 输出
def clean_llm_output(output):
    """
    解析 LLM 输出为 FunctionCall 列表，支持 JSON 数组和多块 JSON
    """
    if not output:
        print("解析错误：LLM 输出为空")
        return []
    
    try:
        # 如果输出是字符串，清理 Markdown 和前后空格
        if isinstance(output, str):
            output = output.strip()
            # 去除 ```json 和 ``` 标记
            output = re.sub(r'^```json\s*|\s*```$', '', output, flags=re.MULTILINE)
            # 解析 JSON（可能是数组或单个对象）
            parsed = json.loads(output)
        else:
            parsed = output
        
        # 统一转换为列表
        if isinstance(parsed, dict):
            parsed = [parsed]
        elif not isinstance(parsed, list):
            print(f"解析错误：JSON 不是数组或对象: {parsed}")
            return []
        
        # 验证每个 FunctionCall
        function_calls = []
        for item in parsed:
            if isinstance(item, dict) and "function" in item and "parameters" in item:
                function_calls.append(item)
            else:
                print(f"解析错误：无效的 FunctionCall 格式: {item}")
        return function_calls
    
    except json.JSONDecodeError as e:
        print(f"JSON 解析失败：{e}")
        # 尝试分割多块 JSON
        try:
            json_blocks = re.split(r'\}\s*\{', output)
            function_calls = []
            for block in json_blocks:
                block = block.strip()
                if not block:
                    continue
                # 补全 JSON 格式
                if not block.startswith('{'):
                    block = '{' + block
                if not block.endswith('}'):
                    block = block + '}'
                try:
                    parsed = json.loads(block)
                    if "function" in parsed and "parameters" in parsed:
                        function_calls.append(parsed)
                except json.JSONDecodeError as e2:
                    print(f"JSON 块解析失败：{e2}，块内容：{block}")
            if function_calls:
                return function_calls
        except Exception as e:
            print(f"多块 JSON 解析失败：{e}")
        
        # 备用正则表达式解析
        try:
            function_matches = re.finditer(r'"function"\s*:\s*"(\w+)"', output)
            city_matches = re.finditer(r'"city"\s*:\s*"([^"]+)"', output)
            days_matches = re.finditer(r'"days"\s*:\s*(\d+)', output)
            
            function_calls = []
            functions = [m.group(1) for m in function_matches]
            cities = [m.group(1) for m in city_matches]
            days = [int(m.group(1)) for m in days_matches]
            
            for i, func in enumerate(functions):
                call = {
                    "function": func,
                    "parameters": {
                        "city": cities[i] if i < len(cities) else None
                    }
                }
                if func == "get_forecast" and i < len(days):
                    call["parameters"]["days"] = days[i]
                if call["parameters"]["city"]:
                    function_calls.append(call)
            return function_calls
        except Exception as e:
            print(f"正则解析失败：{e}")
            return []
    
    except Exception as e:
        print(f"解析 LLM 输出时发生未知错误：{e}")
        return []

# 执行 FunctionCall
def execute_function_call(function_call):
    if not function_call:
        return {"error": "无效的 FunctionCall"}
    
    function_name = function_call.get("function")
    parameters = function_call.get("parameters", {})
    
    city = parameters.get("city")
    if not city:
        return {"error": "未提供城市名称"}
    
    if function_name == "get_weather":
        weather = get_weather(city)
        if not weather:
            return {"error": f"没有 {city} 的当前天气信息"}
        return weather
    
    elif function_name == "get_forecast":
        days = parameters.get("days", 3)  # 默认 3 天
        forecast = get_forecast(city, days)
        if not forecast:
            return {"error": f"没有 {city} 的天气预报信息"}
        return forecast
    
    return {"error": f"不支持的函数: {function_name}"}

# 主函数
def get_weather_info():
    # 获取用户输入
    question = "请问您想查询哪个城市的天气或预报？（例如：上海的天气、杭州未来三天预报）"
    user_input = input(question)
    
    if not user_input.strip():
        return "请输入城市名称！"
    
    # 生成 FunctionCall 提示
    prompt = (
        "将用户输入转为标准的 FunctionCall JSON 数组："
        "1. 如果查询当前天气，使用函数名 get_weather，参数为城市名（city）。"
        "2. 如果查询天气预报，使用函数名 get_forecast，参数为城市名（city）和天数（days，默认为3）。"
        "返回 JSON 数组，包含所有 FunctionCall，例如："
        "[{\"function\": \"get_weather\", \"parameters\": {\"city\": \"上海\"}}, "
        "{\"function\": \"get_forecast\", \"parameters\": {\"city\": \"杭州\", \"days\": 3}}]，"
        "不要包含 Markdown、注释或其他文字。如果输入包含多个查询，返回多个 FunctionCall。"
        "直接返回 JSON 字符串，例如：\"[{\"function\": \"get_weather\", \"parameters\": {\"city\": \"上海\"}}]\"。"
        f"用户输入是：{user_input}"
    )
    
    # 调用 LLM 获取 FunctionCall
    llm_output = call_llm(prompt)
    print("LLM 输出:", llm_output)
    
    # 清洗 LLM 输出
    function_calls = clean_llm_output(llm_output)
    print("解析后的 FunctionCalls:", json.dumps(function_calls, indent=2, ensure_ascii=False))
    
    # 执行所有 FunctionCall
    results = []
    for function_call in function_calls:
        result = execute_function_call(function_call)
        results.append((function_call, result))
    
    print("函数执行结果:", json.dumps([r[1] for r in results], indent=2, ensure_ascii=False))
    
    # 生成用户友好的回答
    if not results:
        return "无法解析任何 FunctionCall"
    
    response = []
    for function_call, result in results:
        if "error" in result:
            response.append(result["error"])
            continue
        
        city = function_call["parameters"]["city"]
        if function_call["function"] == "get_weather":
            weather = result["weather"]
            temperature = result["temperature"]
            response.append(f"今天{city}的天气是{weather}，温度是{temperature}摄氏度")
        
        elif function_call["function"] == "get_forecast":
            forecast = result
            forecast_text = f"{city}未来{len(forecast)}天的天气预报：\n"
            for day in forecast:
                forecast_text += f"{day['day']}：{day['weather']}，温度 {day['temperature']}摄氏度\n"
            response.append(forecast_text.strip())
    
    return "\n".join(response)

if __name__ == "__main__":
    print("最终回答:", get_weather_info())