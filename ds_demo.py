import os
from openai import OpenAI  # 需要安装 openai 这个 Python 包


client = OpenAI(
    api_key="sk-uXJgSOjgTlFCC2BcA172E33231204e94B9A5618089Ab9621",
    base_url="https://test-one-api.summerfarm.top/v1"
)

def call_llm(question):
    response = client.chat.completions.create(
    model='deepseek-v3-250324',  # 这个模型可以自己换v3、r1、都行
    messages=[{"role": "user", "content": f"{question}"}],)
    return response.choices[0].message.content 


if __name__ == '__main__':
    question = "您好，请介绍一下你自己"
    answer = call_llm(question)
    print(answer)